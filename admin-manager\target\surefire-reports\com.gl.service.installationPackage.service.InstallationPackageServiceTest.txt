-------------------------------------------------------------------------------
Test set: com.gl.service.installationPackage.service.InstallationPackageServiceTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 2.628 s <<< FAILURE! - in com.gl.service.installationPackage.service.InstallationPackageServiceTest
testList_WithoutSearchCondition_Success  Time elapsed: 2.615 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForObject' method:
    jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t",
    class java.lang.Long
);
    -> at com.gl.service.installationPackage.service.InstallationPackageService.list(InstallationPackageService.java:59)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForObject(
    "select count(1) from (select 
p.id,
p.version_name,
p.remark,
p.package_url,
p.create_time,
count(pl.id) updateDeviceNum, 
group_concat(pl.device_id) deviceIds from dub_installation_package p left join dub_installation_package_log pl on pl.package_id = p.id where 1=1 GROUP BY p.id  ) t",
    class java.lang.Long
);
      -> at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithoutSearchCondition_Success(InstallationPackageServiceTest.java:171)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.gl.service.installationPackage.service.InstallationPackageServiceTest.testList_WithoutSearchCondition_Success(InstallationPackageServiceTest.java:191)

